import { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { format } from 'date-fns';
import Layout from '../../components/Layout';
import { API_URL, MAIN_DOMAIN } from '../../config';
import { useFarm } from '../../context/FarmContext';
import { useAuth } from '../../context/AuthContext';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

interface Farm {
  id: string;
  name: string;
  billing_email?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  primary_contact_name?: string;
  primary_contact_email?: string;
  primary_contact_phone?: string;
  payment_terms?: string;
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

interface Invoice {
  id: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  status: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  notes: string;
  payment_method: string | null;
  payment_date: string | null;
  payment_amount: number | null;
  document_url: string | null;
  document_name: string | null;
  document_type: string | null;
  document_size: number | null;
  document_uploaded_at: string | null;
  document_uploaded_by: string | null;
  Customer?: Customer;
  recipientFarm?: Farm;
  Farm?: Farm; // The farm that created the invoice (for received invoices)
  InvoiceItems: InvoiceItem[];
}

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface InvoiceEmail {
  id: string;
  invoice_id: string;
  sent_at: string;
  sent_by_user_id: string;
  recipient_email: string;
  status: string;
  User?: User;
}

const InvoiceDetail = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const navigate = useNavigate();
  const { selectedFarm } = useFarm();
  const { user } = useAuth();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Email sending state
  const [showSendEmail, setShowSendEmail] = useState(false);
  const [recipientEmails, setRecipientEmails] = useState<string[]>([]);
  const [newEmail, setNewEmail] = useState('');
  const [sendingEmail, setSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);

  // Reminder sending state
  const [showSendReminder, setShowSendReminder] = useState(false);
  const [reminderRecipientEmails, setReminderRecipientEmails] = useState<string[]>([]);
  const [newReminderEmail, setNewReminderEmail] = useState('');
  const [sendingReminder, setSendingReminder] = useState(false);
  const [reminderSent, setReminderSent] = useState(false);
  const [reminderError, setReminderError] = useState<string | null>(null);
  const [isOverdue, setIsOverdue] = useState(false);

  // Email history state
  const [emailHistory, setEmailHistory] = useState<InvoiceEmail[]>([]);
  const [loadingEmailHistory, setLoadingEmailHistory] = useState(false);

  // Cancel and delete state
  const [cancellingInvoice, setCancellingInvoice] = useState(false);
  const [cancelError, setCancelError] = useState<string | null>(null);
  const [cancelSuccess, setCancelSuccess] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingInvoice, setDeletingInvoice] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Payment recording state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('');
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [recordingPayment, setRecordingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  // Document upload state
  const [showDocumentUploadModal, setShowDocumentUploadModal] = useState(false);
  const [documentFile, setDocumentFile] = useState<File | null>(null);
  const [uploadingDocument, setUploadingDocument] = useState(false);
  const [documentError, setDocumentError] = useState<string | null>(null);
  const [documentSuccess, setDocumentSuccess] = useState(false);
  const [documentDeleting, setDocumentDeleting] = useState(false);

  useEffect(() => {
    const fetchInvoice = async () => {
      if (!invoiceId || !selectedFarm?.id) return;

      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/invoices/${invoiceId}`);
        setInvoice(response.data.invoice);

        // Set initial payment amount to invoice total
        if (response.data.invoice && response.data.invoice.total_amount) {
          setPaymentAmount(response.data.invoice.total_amount.toString());
        }
      } catch (err: any) {
        console.error('Error fetching invoice:', err);
        setError(err.response?.data?.error || 'Failed to load invoice details');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId, selectedFarm]);

  // Fetch email history
  const fetchEmailHistory = async () => {
    if (!invoiceId) return;

    try {
      setLoadingEmailHistory(true);
      const response = await axios.get(`${API_URL}/invoices/${invoiceId}/emails`);
      setEmailHistory(response.data.invoiceEmails);
    } catch (err: any) {
      console.error('Error fetching email history:', err);
      // Don't set an error message for this, as it's not critical
    } finally {
      setLoadingEmailHistory(false);
    }
  };

  // Load email history when invoice is loaded
  useEffect(() => {
    if (invoice) {
      fetchEmailHistory();
    }
  }, [invoice]);

  // Send invoice via email
  const sendInvoiceEmail = async () => {
    if (!invoiceId) return;

    try {
      setSendingEmail(true);
      setEmailError(null);

      // If no additional emails are provided, send to the default customer email
      const emails = recipientEmails.length > 0 ? recipientEmails : [];

      const response = await axios.post(`${API_URL}/invoices/${invoiceId}/send`, {
        recipientEmails: emails
      });

      setEmailSent(true);
      setShowSendEmail(false);
      setRecipientEmails([]);
      setNewEmail('');

      // Refresh email history
      fetchEmailHistory();

      // If invoice was in draft status, refresh the invoice to update the status
      if (invoice?.status === 'draft') {
        const invoiceResponse = await axios.get(`${API_URL}/invoices/${invoiceId}`);
        setInvoice(invoiceResponse.data.invoice);
      }
    } catch (err: any) {
      console.error('Error sending invoice:', err);
      setEmailError(err.response?.data?.error || 'Failed to send invoice');
    } finally {
      setSendingEmail(false);
    }
  };

  // Add email to the list
  const addEmail = () => {
    if (newEmail.trim() && !recipientEmails.includes(newEmail.trim())) {
      setRecipientEmails([...recipientEmails, newEmail.trim()]);
      setNewEmail('');
    }
  };

  // Remove email from the list
  const removeEmail = (email: string) => {
    setRecipientEmails(recipientEmails.filter(e => e !== email));
  };

  // Send invoice reminder
  const sendInvoiceReminder = async () => {
    if (!invoiceId) return;

    try {
      setSendingReminder(true);
      setReminderError(null);

      // If no additional emails are provided, send to the default customer email
      const emails = reminderRecipientEmails.length > 0 ? reminderRecipientEmails : [];

      const response = await axios.post(`${API_URL}/invoices/${invoiceId}/remind`, {
        recipientEmails: emails,
        isOverdue
      });

      setReminderSent(true);
      setShowSendReminder(false);
      setReminderRecipientEmails([]);
      setNewReminderEmail('');

      // Refresh email history
      fetchEmailHistory();
    } catch (err: any) {
      console.error('Error sending invoice reminder:', err);
      setReminderError(err.response?.data?.error || 'Failed to send reminder');
    } finally {
      setSendingReminder(false);
    }
  };

  // Add reminder email to the list
  const addReminderEmail = () => {
    if (newReminderEmail.trim() && !reminderRecipientEmails.includes(newReminderEmail.trim())) {
      setReminderRecipientEmails([...reminderRecipientEmails, newReminderEmail.trim()]);
      setNewReminderEmail('');
    }
  };

  // Remove reminder email from the list
  const removeReminderEmail = (email: string) => {
    setReminderRecipientEmails(reminderRecipientEmails.filter(e => e !== email));
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Record payment
  const recordPayment = async () => {
    if (!invoiceId) return;

    try {
      setRecordingPayment(true);
      setPaymentError(null);

      // Validate inputs
      if (!paymentMethod) {
        setPaymentError('Payment method is required');
        setRecordingPayment(false);
        return;
      }

      if (!paymentAmount || isNaN(parseFloat(paymentAmount)) || parseFloat(paymentAmount) <= 0) {
        setPaymentError('Valid payment amount is required');
        setRecordingPayment(false);
        return;
      }

      if (!paymentDate) {
        setPaymentError('Payment date is required');
        setRecordingPayment(false);
        return;
      }

      // Update the invoice with payment information
      const response = await axios.put(`${API_URL}/invoices/${invoiceId}`, {
        status: 'paid',
        payment_method: paymentMethod,
        payment_amount: parseFloat(paymentAmount),
        payment_date: paymentDate
      });

      // Update the invoice in state
      setInvoice(response.data.invoice);
      setPaymentSuccess(true);
      setShowPaymentModal(false);

      // Reset form
      setPaymentMethod('');
      setPaymentAmount('');
      setPaymentDate(format(new Date(), 'yyyy-MM-dd'));
    } catch (err: any) {
      console.error('Error recording payment:', err);
      setPaymentError(err.response?.data?.error || 'Failed to record payment');
    } finally {
      setRecordingPayment(false);
    }
  };

  // Handle document file selection
  const handleDocumentFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file type (PDF or text)
      const validTypes = ['application/pdf', 'text/plain'];
      if (!validTypes.includes(file.type)) {
        setDocumentError('Only PDF and text files are allowed');
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        setDocumentError('File size exceeds the limit of 10MB');
        return;
      }

      setDocumentFile(file);
      setDocumentError(null);
    }
  };

  // Upload document
  const uploadDocument = async () => {
    if (!invoiceId || !documentFile) return;

    try {
      setUploadingDocument(true);
      setDocumentError(null);

      // Create form data
      const formData = new FormData();
      formData.append('document', documentFile);

      // Upload the document
      const response = await axios.post(
        `${API_URL}/invoices/${invoiceId}/document`, 
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      // Update the invoice in state with the new document information
      setInvoice(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          document_url: response.data.document.url,
          document_name: response.data.document.name,
          document_type: response.data.document.type,
          document_size: response.data.document.size,
          document_uploaded_at: response.data.document.uploaded_at,
          document_uploaded_by: response.data.document.uploaded_by
        };
      });

      setDocumentSuccess(true);
      setShowDocumentUploadModal(false);

      // Reset form
      setDocumentFile(null);
    } catch (err: any) {
      console.error('Error uploading document:', err);
      setDocumentError(err.response?.data?.error || 'Failed to upload document');
    } finally {
      setUploadingDocument(false);
    }
  };

  // Delete document
  const deleteDocument = async () => {
    if (!invoiceId || !invoice?.document_url) return;

    try {
      setDocumentDeleting(true);
      setDocumentError(null);

      // Delete the document
      await axios.delete(`${API_URL}/invoices/${invoiceId}/document`);

      // Update the invoice in state to remove document information
      setInvoice(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          document_url: null,
          document_name: null,
          document_type: null,
          document_size: null,
          document_uploaded_at: null,
          document_uploaded_by: null
        };
      });

      setDocumentSuccess(false);
    } catch (err: any) {
      console.error('Error deleting document:', err);
      setDocumentError(err.response?.data?.error || 'Failed to delete document');
    } finally {
      setDocumentDeleting(false);
    }
  };

  // Cancel invoice
  const cancelInvoice = async () => {
    if (!invoiceId) return;

    try {
      setCancellingInvoice(true);
      setCancelError(null);

      // Call the API to cancel the invoice
      const response = await axios.post(`${API_URL}/invoices/${invoiceId}/cancel`);

      // Update the invoice in state
      setInvoice(response.data.invoice);
      setCancelSuccess(true);
    } catch (err: any) {
      console.error('Error cancelling invoice:', err);
      setCancelError(err.response?.data?.error || 'Failed to cancel invoice');
    } finally {
      setCancellingInvoice(false);
    }
  };

  // Delete invoice
  const deleteInvoice = async () => {
    if (!invoiceId) return;

    try {
      setDeletingInvoice(true);
      setDeleteError(null);

      // Call the API to delete the invoice
      await axios.delete(`${API_URL}/invoices/${invoiceId}`);

      // Redirect to the invoices list
      navigate('/invoices');
    } catch (err: any) {
      console.error('Error deleting invoice:', err);
      setDeleteError(err.response?.data?.error || 'Failed to delete invoice');
      setDeletingInvoice(false);
      setShowDeleteConfirm(false);
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Invoice Details</h1>
        <div className="flex space-x-2">
          <Link
            to="/invoices"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Invoices
          </Link>
          <Link
            to={`/invoices/${invoiceId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Edit Invoice
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {paymentSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Payment recorded successfully!</span>
        </div>
      )}

      {cancelSuccess && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Invoice cancelled successfully!</span>
        </div>
      )}

      {cancelError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{cancelError}</span>
        </div>
      )}

      {deleteError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{deleteError}</span>
        </div>
      )}

      {loading ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading invoice details...</p>
        </div>
      ) : !invoice ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">Invoice not found</p>
          <Link
            to="/invoices"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Invoices
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Invoice Header */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Invoice #{invoice.invoice_number}
                  </h2>
                  <div className="mt-1 flex items-center">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(invoice.status)}`}>
                      {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Amount Due</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(invoice.total_amount)}</p>
                </div>
              </div>
            </div>

            <div className="px-6 py-5 grid grid-cols-1 gap-6 md:grid-cols-3">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Issue Date</h3>
                <p className="mt-1 text-sm text-gray-900">{formatDate(invoice.issue_date)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Due Date</h3>
                <p className="mt-1 text-sm text-gray-900">{formatDate(invoice.due_date)}</p>
              </div>
              {invoice.payment_date && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Payment Date</h3>
                  <p className="mt-1 text-sm text-gray-900">{formatDate(invoice.payment_date)}</p>
                </div>
              )}
            </div>
          </div>

          {/* Customer/Recipient/Sender Information */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                {invoice.Farm ? (
                  <span className="flex items-center">
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 mr-2">
                      From Farm
                    </span>
                    Sender Farm Information
                  </span>
                ) : invoice.Customer ? 'Customer Information' : 'Recipient Farm'}
              </h2>
            </div>

            <div className="px-6 py-5">
              {invoice.Farm ? (
                // This is an invoice received from another farm
                <div>
                  <h3 className="text-base font-semibold text-gray-900">{invoice.Farm.name}</h3>

                  {/* Contact Information */}
                  <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {/* Main Contact Info */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Contact Information</h4>
                      {invoice.Farm.email && (
                        <p className="mt-1 text-sm text-gray-600">
                          <span className="font-medium">Email:</span> {invoice.Farm.email}
                        </p>
                      )}
                      {invoice.Farm.phone && (
                        <p className="mt-1 text-sm text-gray-600">
                          <span className="font-medium">Phone:</span> {invoice.Farm.phone}
                        </p>
                      )}
                      {invoice.Farm.address && (
                        <div className="mt-2 text-sm text-gray-600">
                          <p>{invoice.Farm.address}</p>
                          <p>
                            {invoice.Farm.city}, {invoice.Farm.state} {invoice.Farm.zip_code}
                          </p>
                          {invoice.Farm.country && <p>{invoice.Farm.country}</p>}
                        </div>
                      )}
                    </div>

                    {/* Primary Contact Person */}
                    {(invoice.Farm.primary_contact_name || 
                      invoice.Farm.primary_contact_email || 
                      invoice.Farm.primary_contact_phone) && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Primary Contact</h4>
                        {invoice.Farm.primary_contact_name && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">Name:</span> {invoice.Farm.primary_contact_name}
                          </p>
                        )}
                        {invoice.Farm.primary_contact_email && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">Email:</span> {invoice.Farm.primary_contact_email}
                          </p>
                        )}
                        {invoice.Farm.primary_contact_phone && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">Phone:</span> {invoice.Farm.primary_contact_phone}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Payment Terms */}
                  {invoice.Farm.payment_terms && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-500">Payment Terms</h4>
                      <p className="mt-1 text-sm text-gray-600">{invoice.Farm.payment_terms}</p>
                    </div>
                  )}
                </div>
              ) : invoice.Customer ? (
                <div>
                  <h3 className="text-base font-semibold text-gray-900">{invoice.Customer.name}</h3>
                  <p className="mt-1 text-sm text-gray-600">{invoice.Customer.email}</p>
                  {invoice.Customer.phone && (
                    <p className="mt-1 text-sm text-gray-600">{invoice.Customer.phone}</p>
                  )}
                  {invoice.Customer.address && (
                    <div className="mt-2 text-sm text-gray-600">
                      <p>{invoice.Customer.address}</p>
                      <p>
                        {invoice.Customer.city}, {invoice.Customer.state} {invoice.Customer.zip_code}
                      </p>
                      {invoice.Customer.country && <p>{invoice.Customer.country}</p>}
                    </div>
                  )}
                </div>
              ) : invoice.recipientFarm ? (
                <div>
                  <h3 className="text-base font-semibold text-gray-900">{invoice.recipientFarm.name}</h3>

                  {/* Contact Information */}
                  <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {/* Main Contact Info */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Contact Information</h4>
                      {invoice.recipientFarm.email && (
                        <p className="mt-1 text-sm text-gray-600">
                          <span className="font-medium">Email:</span> {invoice.recipientFarm.email}
                        </p>
                      )}
                      {invoice.recipientFarm.phone && (
                        <p className="mt-1 text-sm text-gray-600">
                          <span className="font-medium">Phone:</span> {invoice.recipientFarm.phone}
                        </p>
                      )}
                      {invoice.recipientFarm.address && (
                        <div className="mt-2 text-sm text-gray-600">
                          <p>{invoice.recipientFarm.address}</p>
                          <p>
                            {invoice.recipientFarm.city}, {invoice.recipientFarm.state} {invoice.recipientFarm.zip_code}
                          </p>
                          {invoice.recipientFarm.country && <p>{invoice.recipientFarm.country}</p>}
                        </div>
                      )}
                    </div>

                    {/* Primary Contact Person */}
                    {(invoice.recipientFarm.primary_contact_name || 
                      invoice.recipientFarm.primary_contact_email || 
                      invoice.recipientFarm.primary_contact_phone) && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Primary Contact</h4>
                        {invoice.recipientFarm.primary_contact_name && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">Name:</span> {invoice.recipientFarm.primary_contact_name}
                          </p>
                        )}
                        {invoice.recipientFarm.primary_contact_email && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">Email:</span> {invoice.recipientFarm.primary_contact_email}
                          </p>
                        )}
                        {invoice.recipientFarm.primary_contact_phone && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">Phone:</span> {invoice.recipientFarm.primary_contact_phone}
                          </p>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Payment Terms */}
                  {invoice.recipientFarm.payment_terms && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-gray-500">Payment Terms</h4>
                      <p className="mt-1 text-sm text-gray-600">{invoice.recipientFarm.payment_terms}</p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No recipient information available</p>
              )}
            </div>
          </div>

          {/* Invoice Items */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Invoice Items</h2>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {invoice.InvoiceItems && invoice.InvoiceItems.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(item.unit_price)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(item.amount)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot className="bg-gray-50">
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                      Subtotal
                    </td>
                    <td className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.subtotal)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                      Tax
                    </td>
                    <td className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                      {formatCurrency(invoice.tax_amount)}
                    </td>
                  </tr>
                  <tr>
                    <td colSpan={3} className="px-6 py-3 text-right text-sm font-bold text-gray-900">
                      Total
                    </td>
                    <td className="px-6 py-3 text-right text-sm font-bold text-gray-900">
                      {formatCurrency(invoice.total_amount)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          {/* Payment Information */}
          {invoice.status === 'paid' && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Payment Information</h2>
              </div>

              <div className="px-6 py-5 grid grid-cols-1 gap-6 md:grid-cols-3">
                {invoice.payment_method && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Payment Method</h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {invoice.payment_method.charAt(0).toUpperCase() + invoice.payment_method.slice(1).replace('_', ' ')}
                    </p>
                  </div>
                )}
                {invoice.payment_date && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Payment Date</h3>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(invoice.payment_date)}</p>
                  </div>
                )}
                {invoice.payment_amount && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Payment Amount</h3>
                    <p className="mt-1 text-sm text-gray-900">{formatCurrency(invoice.payment_amount)}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Notes */}
          {invoice.notes && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-5 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Notes</h2>
              </div>

              <div className="px-6 py-5">
                <p className="text-sm text-gray-600 whitespace-pre-line">{invoice.notes}</p>
              </div>
            </div>
          )}

          {/* Invoice Document */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Invoice Document</h2>
              {!showDocumentUploadModal && (
                <button
                  type="button"
                  onClick={() => {
                    setShowDocumentUploadModal(true);
                    setDocumentError(null);
                    setDocumentFile(null);
                  }}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  {invoice.document_url ? 'Replace Document' : 'Upload Document'}
                </button>
              )}
            </div>

            <div className="px-6 py-5">
              {documentSuccess && !showDocumentUploadModal && (
                <div className="bg-green-50 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">Document uploaded successfully!</span>
                </div>
              )}

              {showDocumentUploadModal ? (
                <div className="space-y-4">
                  {documentError && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                      <span className="block sm:inline">{documentError}</span>
                    </div>
                  )}

                  <div>
                    <label htmlFor="documentFile" className="block text-sm font-medium text-gray-700">
                      Select Document
                    </label>
                    <p className="mt-1 text-sm text-gray-500">
                      Upload a PDF or text document for this invoice. Maximum size: 10MB.
                    </p>
                    <input
                      type="file"
                      id="documentFile"
                      name="documentFile"
                      accept=".pdf,.txt,application/pdf,text/plain"
                      onChange={handleDocumentFileChange}
                      className="mt-2 block w-full text-sm text-gray-500
                        file:mr-4 file:py-2 file:px-4
                        file:rounded-md file:border-0
                        file:text-sm file:font-medium
                        file:bg-primary-50 file:text-primary-700
                        hover:file:bg-primary-100"
                    />
                    {documentFile && (
                      <p className="mt-2 text-sm text-gray-500">
                        Selected file: {documentFile.name} ({(documentFile.size / 1024).toFixed(2)} KB)
                      </p>
                    )}
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowDocumentUploadModal(false)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={uploadDocument}
                      disabled={!documentFile || uploadingDocument}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                    >
                      {uploadingDocument ? 'Uploading...' : 'Upload Document'}
                    </button>
                  </div>
                </div>
              ) : invoice.document_url ? (
                <div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <svg className="h-8 w-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                      </svg>
                      <div className="ml-4">
                        <h3 className="text-sm font-medium text-gray-900">{invoice.document_name}</h3>
                        <p className="text-sm text-gray-500">
                          {invoice.document_size ? `${(invoice.document_size / 1024).toFixed(2)} KB` : ''}
                          {invoice.document_uploaded_at && ` • Uploaded on ${format(new Date(invoice.document_uploaded_at), 'MMM d, yyyy')}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <a
                        href={`${API_URL}/invoices/${invoiceId}/document`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View Document
                      </a>
                      <button
                        type="button"
                        onClick={deleteDocument}
                        disabled={documentDeleting}
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        {documentDeleting ? 'Deleting...' : 'Delete'}
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No document has been uploaded for this invoice yet.</p>
              )}
            </div>
          </div>

          {/* Email Invoice */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Email Invoice</h2>
              {!showSendEmail && !showSendReminder && (
                <button
                  type="button"
                  onClick={() => {
                    setShowSendEmail(true);
                    setEmailError(null);

                    // Get unique emails from email history for this invoice
                    const previousEmails = emailHistory
                      .map(email => email.recipient_email)
                      .filter((email, index, self) => self.indexOf(email) === index);

                    // Include farm's billing email if available
                    if (selectedFarm?.billing_email && !previousEmails.includes(selectedFarm.billing_email)) {
                      previousEmails.push(selectedFarm.billing_email);
                    }

                    // Set the recipient emails
                    if (previousEmails.length > 0) {
                      setRecipientEmails(previousEmails);
                    } else {
                      setRecipientEmails([]);
                    }
                  }}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Send Invoice
                </button>
              )}
            </div>

            <div className="px-6 py-5">
              {emailSent && !showSendEmail && !showSendReminder && (
                <div className="bg-green-50 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">Invoice sent successfully!</span>
                </div>
              )}

              {reminderSent && !showSendEmail && !showSendReminder && (
                <div className="bg-green-50 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                  <span className="block sm:inline">Reminder sent successfully!</span>
                </div>
              )}

              {showSendEmail ? (
                <div className="space-y-4">
                  {emailError && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                      <span className="block sm:inline">{emailError}</span>
                    </div>
                  )}

                  <div>
                    <label htmlFor="recipientEmail" className="block text-sm font-medium text-gray-700">
                      Recipient Emails
                    </label>

                    {/* Default email notice */}
                    <p className="mt-1 text-sm text-gray-500">
                      {invoice.Customer?.email ? 
                        `Default: ${invoice.Customer.email} (will be used if no additional emails are added)` : 
                        'Enter email addresses to send the invoice to'}
                    </p>

                    {/* Email input and add button */}
                    <div className="mt-2 flex">
                      <input
                        type="email"
                        name="newEmail"
                        id="newEmail"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addEmail()}
                        className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="Enter additional email address"
                      />
                      <button
                        type="button"
                        onClick={addEmail}
                        className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Add
                      </button>
                    </div>

                    {/* List of added emails */}
                    {recipientEmails.length > 0 && (
                      <div className="mt-3">
                        <h4 className="text-sm font-medium text-gray-700">Recipients:</h4>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {recipientEmails.map((email, index) => (
                            <div 
                              key={index} 
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {email}
                              <button
                                type="button"
                                onClick={() => removeEmail(email)}
                                className="ml-1.5 inline-flex text-blue-400 hover:text-blue-600 focus:outline-none"
                              >
                                <span className="sr-only">Remove</span>
                                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowSendEmail(false)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={sendInvoiceEmail}
                      disabled={sendingEmail}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                    >
                      {sendingEmail ? 'Sending...' : 'Send Invoice'}
                    </button>
                  </div>
                </div>
              ) : showSendReminder ? (
                <div className="space-y-4">
                  {reminderError && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                      <span className="block sm:inline">{reminderError}</span>
                    </div>
                  )}

                  <div>
                    <label htmlFor="reminderType" className="block text-sm font-medium text-gray-700">
                      Reminder Type
                    </label>
                    <div className="mt-2">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <input
                            id="upcoming"
                            name="reminderType"
                            type="radio"
                            checked={!isOverdue}
                            onChange={() => setIsOverdue(false)}
                            className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
                          />
                          <label htmlFor="upcoming" className="ml-2 block text-sm text-gray-700">
                            Upcoming Payment
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="overdue"
                            name="reminderType"
                            type="radio"
                            checked={isOverdue}
                            onChange={() => setIsOverdue(true)}
                            className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300"
                          />
                          <label htmlFor="overdue" className="ml-2 block text-sm text-gray-700">
                            Overdue Payment
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="reminderRecipientEmail" className="block text-sm font-medium text-gray-700">
                      Recipient Emails
                    </label>

                    {/* Default email notice */}
                    <p className="mt-1 text-sm text-gray-500">
                      {invoice.Customer?.email ? 
                        `Default: ${invoice.Customer.email} (will be used if no additional emails are added)` : 
                        'Enter email addresses to send the reminder to'}
                    </p>

                    {/* Email input and add button */}
                    <div className="mt-2 flex">
                      <input
                        type="email"
                        name="newReminderEmail"
                        id="newReminderEmail"
                        value={newReminderEmail}
                        onChange={(e) => setNewReminderEmail(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addReminderEmail()}
                        className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="Enter additional email address"
                      />
                      <button
                        type="button"
                        onClick={addReminderEmail}
                        className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Add
                      </button>
                    </div>

                    {/* List of added emails */}
                    {reminderRecipientEmails.length > 0 && (
                      <div className="mt-3">
                        <h4 className="text-sm font-medium text-gray-700">Recipients:</h4>
                        <div className="mt-2 flex flex-wrap gap-2">
                          {reminderRecipientEmails.map((email, index) => (
                            <div 
                              key={index} 
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {email}
                              <button
                                type="button"
                                onClick={() => removeReminderEmail(email)}
                                className="ml-1.5 inline-flex text-blue-400 hover:text-blue-600 focus:outline-none"
                              >
                                <span className="sr-only">Remove</span>
                                <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowSendReminder(false)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={sendInvoiceReminder}
                      disabled={sendingReminder}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
                    >
                      {sendingReminder ? 'Sending...' : 'Send Reminder'}
                    </button>
                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-3">Email History</h3>
                  {loadingEmailHistory ? (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"></div>
                      <p className="mt-2 text-sm text-gray-500">Loading email history...</p>
                    </div>
                  ) : emailHistory.length === 0 ? (
                    <p className="text-sm text-gray-500 italic">No emails have been sent for this invoice yet.</p>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Sent By
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Recipient
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {emailHistory.map((email) => (
                            <tr key={email.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {format(new Date(email.sent_at), 'MMM d, yyyy h:mm a')}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {email.User ? `${email.User.first_name} ${email.User.last_name}` : 'System'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {email.recipient_email}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                  {email.status.charAt(0).toUpperCase() + email.status.slice(1)}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            {invoice.status === 'draft' && (
              <button
                type="button"
                onClick={() => {
                  setShowSendEmail(true);
                  setEmailError(null);
                  // Pre-populate with customer email if available
                  if (invoice.Customer?.email) {
                    setRecipientEmail(invoice.Customer.email);
                  } else {
                    setRecipientEmail('');
                  }
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Mark as Sent
              </button>
            )}
            {(invoice.status === 'sent' || invoice.status === 'overdue') && (
              <button
                type="button"
                onClick={() => {
                  setShowPaymentModal(true);
                  setPaymentError(null);
                  setPaymentAmount(invoice.total_amount.toString());
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Record Payment
              </button>
            )}
            {(invoice.status === 'sent' || invoice.status === 'overdue') && (
              <button
                type="button"
                onClick={() => {
                  setShowSendReminder(true);
                  setReminderError(null);
                  setIsOverdue(invoice.status === 'overdue');

                  // Get unique emails from email history for this invoice
                  const previousEmails = emailHistory
                    .map(email => email.recipient_email)
                    .filter((email, index, self) => self.indexOf(email) === index);

                  // Include farm's billing email if available
                  if (selectedFarm?.billing_email && !previousEmails.includes(selectedFarm.billing_email)) {
                    previousEmails.push(selectedFarm.billing_email);
                  }

                  // Set the recipient emails
                  if (previousEmails.length > 0) {
                    setReminderRecipientEmails(previousEmails);
                  } else {
                    setReminderRecipientEmails([]);
                  }
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              >
                Send Reminder
              </button>
            )}
            {invoice.status !== 'cancelled' && invoice.status !== 'paid' && (
              <button
                type="button"
                onClick={cancelInvoice}
                disabled={cancellingInvoice}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {cancellingInvoice ? 'Cancelling...' : 'Cancel Invoice'}
              </button>
            )}
            <button
              type="button"
              onClick={() => setShowDeleteConfirm(true)}
              className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md shadow-sm text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Delete Invoice
            </button>
            <button
              type="button"
              onClick={() => {
                if (invoiceId && selectedFarm?.subdomain) {
                  // Use the farm's subdomain to access the API, which will handle authentication properly
                  window.open(`https://${selectedFarm.subdomain}.${MAIN_DOMAIN}/api/invoices/${invoiceId}/download`, '_blank');
                }
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Download PDF
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Confirm Delete</h3>
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {deleteError && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span className="block sm:inline">{deleteError}</span>
              </div>
            )}

            <div className="mb-4">
              <p className="text-sm text-gray-500">
                Are you sure you want to delete this invoice? This action cannot be undone.
              </p>
              {invoice?.invoice_number && (
                <p className="mt-2 text-sm font-medium text-gray-700">
                  Invoice #{invoice.invoice_number}
                </p>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowDeleteConfirm(false)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={deleteInvoice}
                disabled={deletingInvoice}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
              >
                {deletingInvoice ? 'Deleting...' : 'Delete Invoice'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Record Payment</h3>
              <button
                type="button"
                onClick={() => setShowPaymentModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <span className="sr-only">Close</span>
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {paymentError && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span className="block sm:inline">{paymentError}</span>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700">
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                >
                  <option value="">Select a payment method</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label htmlFor="paymentAmount" className="block text-sm font-medium text-gray-700">
                  Payment Amount
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    name="paymentAmount"
                    id="paymentAmount"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(e.target.value)}
                    className="focus:ring-primary-500 focus:border-primary-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                    placeholder="0.00"
                    step="0.01"
                    min="0"
                  />
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  Invoice total: {invoice ? formatCurrency(invoice.total_amount) : '$0.00'}
                </p>
              </div>

              <div>
                <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700">
                  Payment Date
                </label>
                <input
                  type="date"
                  name="paymentDate"
                  id="paymentDate"
                  value={paymentDate}
                  onChange={(e) => setPaymentDate(e.target.value)}
                  className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowPaymentModal(false)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={recordPayment}
                  disabled={recordingPayment}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  {recordingPayment ? 'Recording...' : 'Record Payment'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default InvoiceDetail;
