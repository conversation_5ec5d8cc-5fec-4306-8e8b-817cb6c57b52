import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import { format } from 'date-fns';
import { API_URL } from '../../config';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

interface Farm {
  id: string;
  name: string;
  billing_email?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  primary_contact_name?: string;
  primary_contact_email?: string;
  primary_contact_phone?: string;
  payment_terms?: string;
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
  taxable: boolean;
}

interface Invoice {
  id: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  status: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  notes: string;
  payment_method: string | null;
  payment_date: string | null;
  payment_amount: number | null;
  document_url: string | null;
  document_name: string | null;
  document_type: string | null;
  document_size: number | null;
  document_uploaded_at: string | null;
  document_uploaded_by: string | null;
  Customer?: Customer;
  recipientFarm?: Farm;
  Farm?: Farm; // The farm that created the invoice (for received invoices)
  InvoiceItems: InvoiceItem[];
}

const PublicInvoiceDetail = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const [searchParams] = useSearchParams();
  const auth = searchParams.get('auth');
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accountBenefits, setAccountBenefits] = useState<string[]>([]);

  useEffect(() => {
    const fetchInvoice = async () => {
      if (!invoiceId || !auth) {
        setError('Invoice ID and authentication code are required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/invoices/${invoiceId}/public?auth=${auth}`);
        setInvoice(response.data.invoice);
        
        // Set account benefits if available
        if (response.data.accountBenefits) {
          setAccountBenefits(response.data.accountBenefits);
        }
      } catch (err: any) {
        console.error('Error fetching invoice:', err);
        setError(err.response?.data?.error || 'Failed to load invoice details');
      } finally {
        setLoading(false);
      }
    };

    fetchInvoice();
  }, [invoiceId, auth]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
        <p className="ml-3 text-gray-500">Loading invoice...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-md p-6 max-w-lg w-full">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-700 mb-4">{error}</p>
          <p className="text-gray-600">
            This invoice may have expired or the link may be invalid. Please contact the sender for a new invoice link.
          </p>
        </div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-md p-6 max-w-lg w-full">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Invoice Not Found</h1>
          <p className="text-gray-700">
            The invoice you're looking for could not be found. Please check the URL and try again.
          </p>
        </div>
      </div>
    );
  }

  // Format dates
  const formattedIssueDate = format(new Date(invoice.issue_date), 'MMM dd, yyyy');
  const formattedDueDate = format(new Date(invoice.due_date), 'MMM dd, yyyy');
  
  // Format payment date if available
  const formattedPaymentDate = invoice.payment_date 
    ? format(new Date(invoice.payment_date), 'MMM dd, yyyy')
    : null;

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Invoice Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Invoice #{invoice.invoice_number}</h1>
              <p className="text-gray-600">
                Status: <span className={`font-semibold ${
                  invoice.status === 'paid' ? 'text-green-600' : 
                  invoice.status === 'cancelled' ? 'text-red-600' :
                  invoice.status === 'overdue' ? 'text-orange-600' : 'text-blue-600'
                }`}>
                  {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                </span>
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <p className="text-gray-600">Issue Date: {formattedIssueDate}</p>
              <p className="text-gray-600">Due Date: {formattedDueDate}</p>
              {formattedPaymentDate && (
                <p className="text-gray-600">Payment Date: {formattedPaymentDate}</p>
              )}
            </div>
          </div>

          {/* Farm and Customer Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-2">From</h2>
              <div className="text-gray-600">
                <p className="font-medium">{invoice.Farm?.name}</p>
                {invoice.Farm?.address && <p>{invoice.Farm.address}</p>}
                {(invoice.Farm?.city || invoice.Farm?.state || invoice.Farm?.zip_code) && (
                  <p>
                    {invoice.Farm.city}{invoice.Farm.city && invoice.Farm.state ? ', ' : ''}
                    {invoice.Farm.state} {invoice.Farm.zip_code}
                  </p>
                )}
                {invoice.Farm?.country && <p>{invoice.Farm.country}</p>}
                {invoice.Farm?.phone && <p>Phone: {invoice.Farm.phone}</p>}
                {invoice.Farm?.billing_email && <p>Email: {invoice.Farm.billing_email}</p>}
              </div>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-2">To</h2>
              <div className="text-gray-600">
                <p className="font-medium">{invoice.Customer?.name}</p>
                {invoice.Customer?.address && <p>{invoice.Customer.address}</p>}
                {(invoice.Customer?.city || invoice.Customer?.state || invoice.Customer?.zip_code) && (
                  <p>
                    {invoice.Customer.city}{invoice.Customer.city && invoice.Customer.state ? ', ' : ''}
                    {invoice.Customer.state} {invoice.Customer.zip_code}
                  </p>
                )}
                {invoice.Customer?.country && <p>{invoice.Customer.country}</p>}
                {invoice.Customer?.phone && <p>Phone: {invoice.Customer.phone}</p>}
                {invoice.Customer?.email && <p>Email: {invoice.Customer.email}</p>}
              </div>
            </div>
          </div>

          {/* Invoice Items */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-3">Invoice Items</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th className="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th className="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                    <th className="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {invoice.InvoiceItems.map((item) => (
                    <tr key={item.id} className="border-b border-gray-200 hover:bg-gray-50">
                      <td className="py-3 px-4 text-sm text-gray-700">{item.description}</td>
                      <td className="py-3 px-4 text-sm text-gray-700 text-right">{item.quantity}</td>
                      <td className="py-3 px-4 text-sm text-gray-700 text-right">${item.unit_price.toFixed(2)}</td>
                      <td className="py-3 px-4 text-sm text-gray-700 text-right">${item.amount.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="bg-gray-50">
                    <td colSpan={3} className="py-2 px-4 text-sm font-medium text-gray-700 text-right">Subtotal</td>
                    <td className="py-2 px-4 text-sm font-medium text-gray-700 text-right">${invoice.subtotal.toFixed(2)}</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td colSpan={3} className="py-2 px-4 text-sm font-medium text-gray-700 text-right">Tax</td>
                    <td className="py-2 px-4 text-sm font-medium text-gray-700 text-right">${invoice.tax_amount.toFixed(2)}</td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td colSpan={3} className="py-2 px-4 text-sm font-bold text-gray-800 text-right">Total</td>
                    <td className="py-2 px-4 text-sm font-bold text-gray-800 text-right">${invoice.total_amount.toFixed(2)}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          {/* Notes */}
          {invoice.notes && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-2">Notes</h2>
              <div className="bg-gray-50 p-4 rounded-md text-gray-700 whitespace-pre-wrap">
                {invoice.notes}
              </div>
            </div>
          )}

          {/* Payment Information */}
          {invoice.status === 'paid' && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-2">Payment Information</h2>
              <div className="bg-green-50 p-4 rounded-md">
                <p className="text-green-700">
                  <span className="font-medium">Payment Method:</span> {invoice.payment_method}
                </p>
                <p className="text-green-700">
                  <span className="font-medium">Payment Amount:</span> ${invoice.payment_amount?.toFixed(2)}
                </p>
                <p className="text-green-700">
                  <span className="font-medium">Payment Date:</span> {formattedPaymentDate}
                </p>
              </div>
            </div>
          )}

          {/* Account Benefits */}
          {accountBenefits.length > 0 && (
            <div className="mt-8 p-4 bg-blue-50 rounded-md">
              <h2 className="text-lg font-semibold text-blue-800 mb-2">Create an Account for More Features</h2>
              <ul className="list-disc pl-5 text-blue-700">
                {accountBenefits.map((benefit, index) => (
                  <li key={index} className="mb-1">{benefit}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicInvoiceDetail;